<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unit 15 - Success Reading App (Comic Style)</title>
    <style>
        body {
            font-family: 'Comic Sans MS', cursive;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            background: #8e44ad;
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            display: none;
        }
        
        .section.active {
            display: block;
        }
        
        .controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .text-container {
            background: #fff;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            font-size: 18px;
            line-height: 1.8;
            margin-bottom: 20px;
            min-height: 300px;
        }
        
        .highlight {
            background-color: #87CEEB;
            padding: 2px 4px;
            border-radius: 3px;
            transition: all 0.1s ease;
        }
        
        .question {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #8e44ad;
        }
        
        .options {
            margin-top: 15px;
        }
        
        .option {
            display: block;
            margin: 8px 0;
            padding: 10px;
            background: white;
            border: 2px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .option:hover {
            border-color: #8e44ad;
            transform: translateX(5px);
        }
        
        .option.selected {
            background: #e8f4fd;
            border-color: #3498db;
        }
        
        .option.correct {
            background: #d4edda;
            border-color: #28a745;
        }
        
        .option.incorrect {
            background: #f8d7da;
            border-color: #dc3545;
        }
        
        .btn {
            background: #8e44ad;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #732d91;
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .word-box {
            background: #e8f4fd;
            border: 2px dashed #3498db;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .word-bank {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }
        
        .word-item {
            background: #3498db;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .word-item:hover {
            background: #2980b9;
            transform: scale(1.05);
        }
        
        .gap {
            background: #fff3cd;
            border: 2px dashed #ffc107;
            padding: 5px 10px;
            margin: 0 2px;
            cursor: pointer;
            border-radius: 5px;
            display: inline-block;
            min-width: 60px;
            text-align: center;
        }
        
        .gap.revealed {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
            font-weight: bold;
        }
        
        .puzzle-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        
        .puzzle-level {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #ddd;
        }
        
        .drag-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        
        .sentence-parts {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            min-height: 400px;
        }
        
        .sentence-item {
            background: #3498db;
            color: white;
            padding: 15px;
            margin: 10px;
            border-radius: 8px;
            cursor: move;
            user-select: none;
            transition: all 0.3s ease;
        }
        
        .sentence-item:hover {
            background: #2980b9;
            transform: scale(1.02);
        }
        
        .sentence-item.dragging {
            opacity: 0.5;
        }
        
        .drop-zone {
            background: #fff;
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            min-height: 60px;
            transition: all 0.3s ease;
        }
        
        .drop-zone.drag-over {
            border-color: #8e44ad;
            background: #f8f4fd;
        }
        
        .navigation {
            text-align: center;
            margin-top: 30px;
        }
        
        .progress {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #8e44ad, #3498db);
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .score-display {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #8e44ad;
            margin: 20px 0;
        }
        
        input, select {
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        input:focus, select:focus {
            outline: none;
            border-color: #8e44ad;
        }

        /* New comic style card layout */
        .comic-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .comic-card {
            background: #fff;
            border: 3px solid #333;
            border-radius: 10px;
            padding: 15px;
            position: relative;
            min-height: 200px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .card-number {
            position: absolute;
            top: -15px;
            left: -15px;
            width: 30px;
            height: 30px;
            background: #8e44ad;
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            font-size: 18px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .character-name {
            font-weight: bold;
            color: #3498db;
            margin-right: 10px;
            display: inline-block;
            min-width: 80px;
        }
        
        .dialog-line {
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .verb-box {
            background: linear-gradient(135deg, #9b59b6 0%, #3498db 100%);
            color: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .verb-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        .verb-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }
        
        .verb {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Unit 15 - Success</h1>
            <p>Read about amazing success stories and practice your skills!</p>
        </div>

        <!-- Speed Reading Section -->
        <div id="speed-reading" class="section active">
            <h2>📖 Speed Reading Practice</h2>
            <div class="controls">
                <label>Words per minute: <input type="number" id="wpm" value="200" min="50" max="800"></label>
                <label>Highlight: 
                    <select id="highlight-mode">
                        <option value="1">1 word</option>
                        <option value="2">2 words</option>
                        <option value="3">3 words</option>
                        <option value="sentence">Full sentence</option>
                    </select>
                </label>
                <button class="btn" onclick="startReading()">▶️ Start</button>
                <button class="btn" onclick="pauseReading()">⸮️ Pause</button>
                <button class="btn" onclick="resetReading()">🔄 Reset</button>
            </div>
            
            <div class="verb-box">
                <div class="verb-title">Verbs in this story:</div>
                <div class="verb-list" id="verb-list"></div>
            </div>
            
            <div class="text-container">
                <div class="comic-grid" id="reading-text"></div>
            </div>
            
            <div class="navigation">
                <button class="btn" onclick="showSection('comprehension')">Next: Comprehension Questions →</button>
            </div>
        </div>

        <!-- Comprehension Questions Section -->
        <div id="comprehension" class="section">
            <h2>🧠 Comprehension Questions</h2>
            <div id="questions-container"></div>
            <div class="navigation">
                <button class="btn" onclick="checkAnswers()">✅ Check Answers</button>
                <button class="btn" onclick="showAnswers()">👁️ Show Answers</button>
                <button class="btn" onclick="showSection('gap-fill')">Next: Gap Fill →</button>
            </div>
            <div class="score-display" id="comprehension-score"></div>
        </div>

        <!-- Gap Fill Section -->
        <div id="gap-fill" class="section">
            <h2>🔤 Fill in the Gaps</h2>
            <div class="word-box">
                <h3>Word Bank</h3>
                <div class="word-bank" id="word-bank"></div>
            </div>
            <div class="text-container">
                <div class="comic-grid" id="gap-text"></div>
            </div>
            <div class="navigation">
                <button class="btn" onclick="revealAllGaps()">🔍 Reveal All Answers</button>
                <button class="btn" onclick="resetGapFill()">🔄 Reset Gap Fill</button>
                <button class="btn" onclick="showSection('capital-letters')">Next: Capital Letters →</button>
            </div>
        </div>

        <!-- Capital Letters Section -->
        <div id="capital-letters" class="section">
            <h2>🔤 Capital Letters Practice</h2>
            <div class="question">
                <h3>Theory: We use capital letters for:</h3>
                <ul>
                    <li>Starting a sentence, e.g. <em>Sunday</em></li>
                    <li>People's names, e.g. <em>Sally</em></li>
                    <li>Place names, e.g. <em>Manchester</em></li>
                    <li>Languages, e.g. <em>Spanish, German</em></li>
                    <li>Days & months, e.g. <em>Monday, July</em></li>
                    <li>Nationalities, e.g. <em>I'm British</em></li>
                </ul>
            </div>
            <div id="capital-questions-container"></div>
            <div class="navigation">
                <button class="btn" onclick="checkCapitalAnswers()">✅ Check Answers</button>
                <button class="btn" onclick="showCapitalAnswers()">👁️ Show Answers</button>
                <button class="btn" onclick="showSection('sentence-order')">Next: Sentence Order →</button>
            </div>
            <div class="score-display" id="capital-score"></div>
        </div>

        <!-- Sentence Ordering Section -->
        <div id="sentence-order" class="section">
            <h2>🔄 Put Cards in Order</h2>
            <div class="drag-container">
                <div class="sentence-parts">
                    <h3>Drag from here:</h3>
                    <div id="sentence-source"></div>
                </div>
                <div class="sentence-parts">
                    <h3>Drop in correct order:</h3>
                    <div id="sentence-target"></div>
                </div>
            </div>
            <div class="navigation">
                <button class="btn" onclick="checkOrder()">✅ Check Order</button>
                <button class="btn" onclick="showSection('interview')">Next: Interview Practice →</button>
            </div>
        </div>

        <!-- Interview Practice Section -->
        <div id="interview" class="section">
            <h2>🎤 Interview Practice</h2>
            <div class="question">
                <h3>Practice answering these interview questions about success:</h3>
                <p><em>Click on each question to see sample answers!</em></p>
            </div>
            <div id="interview-questions-container"></div>
            <div class="navigation">
                <button class="btn" onclick="showFinalResults()">🎉 Show Final Results</button>
            </div>
        </div>

        <!-- Final Results -->
        <div id="final-results" class="section">
            <h2>🎊 Congratulations!</h2>
            <div class="score-display" id="final-score-display"></div>
            <div class="navigation">
                <button class="btn" onclick="restartApp()">🔄 Start Over</button>
            </div>
        </div>

        <div class="progress">
            <div class="progress-bar" id="progress-bar" style="width: 14.28%"></div>
        </div>
    </div>

    <script>
        // Comic-style dialogue content
        const comicDialogue = [
            {
                cardNumber: 1,
                characters: [
                    { name: "Sarah", text: "Did you hear about J.K. Rowling? She's such an inspiration to me!" },
                    { name: "Tom", text: "I know she wrote Harry Potter, but what's her success story?" },
                    { name: "Sarah", text: "Well, she started as a secretary but wasn't organized enough for the job." }
                ]
            },
            {
                cardNumber: 2,
                characters: [
                    { name: "Tom", text: "Really? What did she do before becoming a writer?" },
                    { name: "Sarah", text: "She studied French at university and wanted to be a ballerina as a child." },
                    { name: "Tom", text: "Interesting! How did she come up with Harry Potter?" }
                ]
            },
            {
                cardNumber: 3,
                characters: [
                    { name: "Sarah", text: "She thought of it on a train journey from Manchester to London!" },
                    { name: "Tom", text: "That's amazing! And Brad Pitt has a similar story, right?" },
                    { name: "Sarah", text: "Yes! He studied journalism but dropped out before graduating." }
                ]
            },
            {
                cardNumber: 4,
                characters: [
                    { name: "Tom", text: "What did he do in LA before becoming famous?" },
                    { name: "Sarah", text: "He worked odd jobs, like a refrigerator delivery boy and limousine driver!" },
                    { name: "Tom", text: "So both of them found success in unexpected ways. That's inspiring!" }
                ]
            }
        ];

        // Extract all verbs from the dialogue for the verb box
        const verbs = [
            "hear", "is", "know", "wrote", "started", "wasn't", "did", "do", "studied", 
            "wanted", "be", "come", "thought", "has", "dropped", "graduating", "worked", 
            "becoming", "found", "inspiring"
        ];

        // Global variables
        let readingTimer;
        let currentSentenceIndex = 0;
        let isReading = false;
        let comprehensionAnswers = [];
        let allSentences = [];
        let userScores = {
            comprehension: 0,
            capital: 0,
            total: 0
        };

        // Initialize the app
        function initializeApp() {
            setupVerbList();
            setupSpeedReading();
            setupComprehensionQuestions();
            setupGapFill();
            setupCapitalLetters();
            setupSentenceOrder();
            setupInterview();
        }

        // Setup verb list
        function setupVerbList() {
            const verbList = document.getElementById('verb-list');
            verbs.forEach(verb => {
                const verbSpan = document.createElement('span');
                verbSpan.className = 'verb';
                verbSpan.textContent = verb;
                verbList.appendChild(verbSpan);
            });
        }

        // Speed Reading Functions
        function setupSpeedReading() {
            // Create an array of all sentences for the speed reading
            allSentences = [];
            comicDialogue.forEach(card => {
                card.characters.forEach(char => {
                    allSentences.push({
                        cardNumber: card.cardNumber,
                        characterName: char.name,
                        text: char.text
                    });
                });
            });
            
            // Render comic cards
            renderComicCards('reading-text', comicDialogue);
        }

        function renderComicCards(containerId, cardData) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
            
            cardData.forEach(card => {
                const cardDiv = document.createElement('div');
                cardDiv.className = 'comic-card';
                
                // Add card number
                const numberDiv = document.createElement('div');
                numberDiv.className = 'card-number';
                numberDiv.textContent = card.cardNumber;
                cardDiv.appendChild(numberDiv);
                
                // Add character dialogues
                card.characters.forEach(character => {
                    const dialogDiv = document.createElement('div');
                    dialogDiv.className = 'dialog-line';
                    
                    const nameSpan = document.createElement('span');
                    nameSpan.className = 'character-name';
                    nameSpan.textContent = character.name;
                    
                    dialogDiv.appendChild(nameSpan);
                    dialogDiv.appendChild(document.createTextNode(character.text));
                    
                    cardDiv.appendChild(dialogDiv);
                });
                
                container.appendChild(cardDiv);
            });
        }

        function startReading() {
            if (isReading) return;
            
            isReading = true;
            const wpm = parseInt(document.getElementById('wpm').value);
            const highlightMode = document.getElementById('highlight-mode').value;
            const interval = 60000 / wpm; // milliseconds per word
            
            currentSentenceIndex = 0;
            
            readingTimer = setInterval(() => {
                highlightSentence(highlightMode);
            }, interval);
        }

        function highlightSentence(mode) {
            if (currentSentenceIndex >= allSentences.length) {
                pauseReading();
                return;
            }
            
            const currentSentence = allSentences[currentSentenceIndex];
            
            // Reset all highlights
            const allDialogLines = document.querySelectorAll('.dialog-line');
            allDialogLines.forEach(line => {
                line.classList.remove('highlight');
            });
            
            // Find the current dialog line to highlight
            const cardIndex = currentSentence.cardNumber - 1;
            const characterIndex = comicDialogue[cardIndex].characters.findIndex(
                char => char.name === currentSentence.characterName
            );
            
            if (characterIndex !== -1) {
                const cards = document.querySelectorAll('#reading-text .comic-card');
                const targetCard = cards[cardIndex];
                const dialogLines = targetCard.querySelectorAll('.dialog-line');
                const targetLine = dialogLines[characterIndex];
                
                if (targetLine) {
                    targetLine.classList.add('highlight');
                }
            }
            
            currentSentenceIndex++;
        }

        function pauseReading() {
            isReading = false;
            if (readingTimer) {
                clearInterval(readingTimer);
            }
        }

        function resetReading() {
            pauseReading();
            currentSentenceIndex = 0;
            
            // Remove all highlights
            const allDialogLines = document.querySelectorAll('.dialog-line');
            allDialogLines.forEach(line => {
                line.classList.remove('highlight');
            });
        }

        // Comprehension Questions
        function setupComprehensionQuestions() {
            const questions = [
                {
                    question: "What was J.K. Rowling's job before she became famous?",
                    options: ["A teacher", "A secretary", "A journalist", "A ballerina"],
                    correct: 1
                },
                {
                    question: "What did J.K. Rowling want to be as a child?",
                    options: ["A writer", "A ballerina", "A secretary", "A teacher"],
                    correct: 1
                },
                {
                    question: "Where did J.K. Rowling get the idea for Harry Potter?",
                    options: ["At home", "At work", "On a train", "At school"],
                    correct: 2
                },
                {
                    question: "What subject did J.K. Rowling study at university?",
                    options: ["English", "Journalism", "French", "Literature"],
                    correct: 2
                },
                {
                    question: "Why did Brad Pitt drop out of university?",
                    options: ["He failed his exams", "He wasn't interested in journalism", "He needed money", "He wanted to be an actor immediately"],
                    correct: 1
                },
                {
                    question: "What was one of Brad Pitt's first jobs in LA?",
                    options: ["Actor", "Journalist", "Refrigerator delivery boy", "Teacher"],
                    correct: 2
                },
                {
                    question: "What do both J.K. Rowling and Brad Pitt have in common?",
                    options: ["They both studied journalism", "They both became famous overnight", "They both found success in unexpected ways", "They both wanted to be dancers"],
                    correct: 2
                }
            ];

            const container = document.getElementById('questions-container');
            questions.forEach((q, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question';
                questionDiv.innerHTML = `
                    <h3>Question ${index + 1}</h3>
                    <p><strong>${q.question}</strong></p>
                    <div class="options">
                        ${q.options.map((option, optIndex) => `
                            <label class="option" onclick="selectOption(${index}, ${optIndex})">
                                <input type="radio" name="q${index}" value="${optIndex}" style="margin-right: 10px;">
                                ${String.fromCharCode(97 + optIndex)}) ${option}
                            </label>
                        `).join('')}
                    </div>
                `;
                container.appendChild(questionDiv);
            });

            comprehensionAnswers = questions.map(q => q.correct);
        }

        function selectOption(questionIndex, optionIndex) {
            const options = document.querySelectorAll(`input[name="q${questionIndex}"]`);
            options.forEach(opt => opt.checked = false);
            options[optionIndex].checked = true;
            
            const labels = document.querySelectorAll(`input[name="q${questionIndex}"]`).forEach((input, idx) => {
                input.parentElement.classList.remove('selected');
            });
            options[optionIndex].parentElement.classList.add('selected');
        }

        function checkAnswers() {
            let correct = 0;
            for (let i = 0; i < comprehensionAnswers.length; i++) {
                const selected = document.querySelector(`input[name="q${i}"]:checked`);
                if (selected && parseInt(selected.value) === comprehensionAnswers[i]) {
                    correct++;
                    selected.parentElement.classList.add('correct');
                } else if (selected) {
                    selected.parentElement.classList.add('incorrect');
                }
            }
            
            userScores.comprehension = Math.round((correct / comprehensionAnswers.length) * 100);
            document.getElementById('comprehension-score').textContent = 
                `Score: ${correct}/${comprehensionAnswers.length} (${userScores.comprehension}%)`;
        }

        function showAnswers() {
            for (let i = 0; i < comprehensionAnswers.length; i++) {
                const correctOption = document.querySelector(`input[name="q${i}"][value="${comprehensionAnswers[i]}"]`);
                if (correctOption) {
                    correctOption.parentElement.classList.add('correct');
                }
            }
        }

        // Gap Fill Functions
        function setupGapFill() {
            // Define a new comic dialogue with gaps
            const gapComicDialogue = [
                {
                    cardNumber: 1,
                    characters: [
                        { name: "Holly", text: "That's _____ You _____ have pasta. We have pasta _____ time." },
                        { name: "Waiter", text: "Usually? But you're our _____ customers!" },
                        { name: "Max", text: "Really? But this _____ Ken's Café, _____ it?" }
                    ]
                },
                {
                    cardNumber: 2,
                    characters: [
                        { name: "Waiter", text: "Ken's Café is _____ door. This is The Pizza _____. We only _____ today." },
                        { name: "Dad", text: "So that's _____ it's different! Well, let's _____it." },
                        { name: "Waiter", text: "You're our first customers. Have _____ ice creams!" }
                    ]
                },
                {
                    cardNumber: 3,
                    characters: [
                        { name: "Max", text: "We often _____ to this restaurant. The food here _____ great!" },
                        { name: "Holly", text: "Look, Max! All the waiters _____ wearing green today. They usually _____ blue uniforms." }
                    ]
                },
                {
                    cardNumber: 4,
                    characters: [
                        { name: "Dad", text: "Can I _____ a bowl of pasta and some salad, _____?" },
                        { name: "Waiter", text: "Sorry, we _____ got any pasta. But on the menu, we've _____ a selection of pizzas. Look!" },
                        { name: "Holly", text: "Wow! _____ you!" }
                    ]
                }
            ];
            
            // Words for the gaps
            const gapWords = [
                "hear", "inspiration", "know", "started", "organized",
                "do", "writer", "studied", "ballerina", "come",
                "thought", "Manchester", "similar", "journalism", "graduating",
                "becoming", "worked", "delivery", "found", "inspiring"
            ];
            
            // Render the gap comic cards
            renderGapComicCards('gap-text', gapComicDialogue);
            
            // Setup word bank
            const wordBank = document.getElementById('word-bank');
            wordBank.innerHTML = '';
            
            // Shuffle the words for the word bank
            const shuffledWords = [...gapWords].sort(() => Math.random() - 0.5);
            shuffledWords.forEach((word, index) => {
                const wordElement = document.createElement('span');
                wordElement.className = 'word-item';
                wordElement.textContent = word;
                wordElement.dataset.word = word;
                wordElement.onclick = function() {
                    fillSelectedGap(word);
                };
                wordBank.appendChild(wordElement);
            });
            
            // Store gap words for reference
            window.gapAnswers = gapWords;
        }
        
        function renderGapComicCards(containerId, cardData) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
            
            let gapIndex = 0;
            
            cardData.forEach(card => {
                const cardDiv = document.createElement('div');
                cardDiv.className = 'comic-card';
                
                // Add card number
                const numberDiv = document.createElement('div');
                numberDiv.className = 'card-number';
                numberDiv.textContent = card.cardNumber;
                cardDiv.appendChild(numberDiv);
                
                // Add character dialogues with gaps
                card.characters.forEach(character => {
                    const dialogDiv = document.createElement('div');
                    dialogDiv.className = 'dialog-line';
                    
                    const nameSpan = document.createElement('span');
                    nameSpan.className = 'character-name';
                    nameSpan.textContent = character.name;
                    dialogDiv.appendChild(nameSpan);
                    
                    // Process text to add gaps
                    const textParts = character.text.split('_____');
                    for (let i = 0; i < textParts.length; i++) {
                        dialogDiv.appendChild(document.createTextNode(textParts[i]));
                        
                        if (i < textParts.length - 1) {
                            const gapSpan = document.createElement('span');
                            gapSpan.className = 'gap';
                            gapSpan.textContent = (gapIndex + 1);
                            gapSpan.dataset.index = gapIndex;
                            gapSpan.onclick = function() {
                                selectGap(this.dataset.index);
                            };
                            dialogDiv.appendChild(gapSpan);
                            gapIndex++;
                        }
                    }
                    
                    cardDiv.appendChild(dialogDiv);
                });
                
                container.appendChild(cardDiv);
            });
        }
        
        let selectedGapIndex = -1;
        
        function selectGap(index) {
            // Deselect all gaps
            document.querySelectorAll('.gap').forEach(gap => {
                gap.style.border = '2px dashed #ffc107';
            });
            
            // Select the clicked gap
            const selectedGap = document.querySelector(`.gap[data-index="${index}"]`);
            if (selectedGap && !selectedGap.classList.contains('revealed')) {
                selectedGap.style.border = '2px solid #8e44ad';
                selectedGapIndex = parseInt(index);
            } else {
                selectedGapIndex = -1;
            }
        }
        
        function fillSelectedGap(word) {
            if (selectedGapIndex === -1) return;
            
            const selectedGap = document.querySelector(`.gap[data-index="${selectedGapIndex}"]`);
            if (selectedGap && !selectedGap.classList.contains('revealed')) {
                selectedGap.textContent = word;
                selectedGap.classList.add('revealed');
                selectedGap.style.border = '2px solid #28a745';
                
                // Remove the word from the word bank
                const wordItem = document.querySelector(`.word-item[data-word="${word}"]`);
                if (wordItem) {
                    wordItem.style.display = 'none';
                }
                
                // Reset selection
                selectedGapIndex = -1;
            }
        }
        
        function revealAllGaps() {
            const allGaps = document.querySelectorAll('.gap');
            allGaps.forEach((gap, index) => {
                if (index < window.gapAnswers.length) {
                    gap.textContent = window.gapAnswers[index];
                    gap.classList.add('revealed');
                    gap.style.border = '2px solid #28a745';
                }
            });
            
            // Hide all word bank items
            document.querySelectorAll('.word-item').forEach(item => {
                item.style.display = 'none';
            });
        }
        
        function resetGapFill() {
            // Reset gap fill
            setupGapFill();
            
            // Reset selection
            selectedGapIndex = -1;
        }

        // Capital Letters Functions
        function setupCapitalLetters() {
            const capitalQuestions = [
                {
                    question: "j.k. rowling is the author of harry potter.",
                    correct: "J.K. Rowling is the author of Harry Potter.",
                    options: ["j.k. rowling is the author of harry potter.", "J.K. Rowling is the author of Harry Potter.", "j.k. rowling is the Author of Harry Potter.", "J.K. Rowling is the Author of Harry Potter."]
                },
                {
                    question: "brad pitt studied journalism at missouri university.",
                    correct: "Brad Pitt studied journalism at Missouri University.",
                    options: ["brad pitt studied journalism at missouri university.", "Brad Pitt studied journalism at Missouri University.", "Brad Pitt Studied Journalism at Missouri University.", "brad pitt studied Journalism at Missouri University."]
                },
                {
                    question: "she thought of harry potter on a train to london.",
                    correct: "She thought of Harry Potter on a train to London.",
                    options: ["she thought of harry potter on a train to london.", "She thought of Harry Potter on a train to London.", "She Thought of Harry Potter on a Train to London.", "She thought of harry potter on a train to London."]
                },
                {
                    question: "sarah and tom discussed famous success stories.",
                    correct: "Sarah and Tom discussed famous success stories.",
                    options: ["sarah and tom discussed famous success stories.", "Sarah and Tom discussed famous success stories.", "Sarah and Tom Discussed Famous Success Stories.", "sarah and tom discussed Famous Success Stories."]
                },
                {
                    question: "he worked as a delivery boy in los angeles.",
                    correct: "He worked as a delivery boy in Los Angeles.",
                    options: ["he worked as a delivery boy in los angeles.", "He worked as a delivery boy in Los Angeles.", "He Worked as a Delivery Boy in Los Angeles.", "he worked as a delivery boy in Los Angeles."]
                }
            ];

            const container = document.getElementById('capital-questions-container');
            capitalQuestions.forEach((q, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question';
                questionDiv.innerHTML = `
                    <h3>Question ${index + 1}</h3>
                    <p><strong>Add capital letters to: "${q.question}"</strong></p>
                    <div class="options">
                        ${q.options.map((option, optIndex) => `
                            <label class="option" onclick="selectCapitalOption(${index}, ${optIndex})">
                                <input type="radio" name="capital${index}" value="${optIndex}" style="margin-right: 10px;">
                                ${String.fromCharCode(97 + optIndex)}) ${option}
                            </label>
                        `).join('')}
                    </div>
                `;
                container.appendChild(questionDiv);
            });

            window.capitalAnswers = capitalQuestions.map(q => q.options.indexOf(q.correct));
        }

        function selectCapitalOption(questionIndex, optionIndex) {
            const options = document.querySelectorAll(`input[name="capital${questionIndex}"]`);
            options.forEach(opt => {
                opt.checked = false;
                opt.parentElement.classList.remove('selected');
            });
            options[optionIndex].checked = true;
            options[optionIndex].parentElement.classList.add('selected');
        }

        function checkCapitalAnswers() {
            let correct = 0;
            for (let i = 0; i < window.capitalAnswers.length; i++) {
                const selected = document.querySelector(`input[name="capital${i}"]:checked`);
                if (selected && parseInt(selected.value) === window.capitalAnswers[i]) {
                    correct++;
                    selected.parentElement.classList.add('correct');
                } else if (selected) {
                    selected.parentElement.classList.add('incorrect');
                }
            }

            userScores.capital = Math.round((correct / window.capitalAnswers.length) * 100);
            document.getElementById('capital-score').textContent =
                `Score: ${correct}/${window.capitalAnswers.length} (${userScores.capital}%)`;
        }

        function showCapitalAnswers() {
            for (let i = 0; i < window.capitalAnswers.length; i++) {
                const correctOption = document.querySelector(`input[name="capital${i}"][value="${window.capitalAnswers[i]}"]`);
                if (correctOption) {
                    correctOption.parentElement.classList.add('correct');
                }
            }
        }

        // Sentence Ordering Functions
        function setupSentenceOrder() {
            const comicCards = [
                {
                    cardNumber: 1,
                    text: "Sarah asks Tom if he has heard about J.K. Rowling and mentions she was a secretary.",
                    correctPosition: 0
                },
                {
                    cardNumber: 2,
                    text: "Tom asks about her background and Sarah explains she studied French and wanted to be a ballerina.",
                    correctPosition: 1
                },
                {
                    cardNumber: 3,
                    text: "Sarah explains Rowling thought of Harry Potter on a train and Tom asks about Brad Pitt.",
                    correctPosition: 2
                },
                {
                    cardNumber: 4,
                    text: "They discuss Brad Pitt's odd jobs before fame and how both found success in unexpected ways.",
                    correctPosition: 3
                }
            ];

            const shuffled = [...comicCards].sort(() => Math.random() - 0.5);
            const sourceContainer = document.getElementById('sentence-source');
            const targetContainer = document.getElementById('sentence-target');

            sourceContainer.innerHTML = '';
            targetContainer.innerHTML = '';

            shuffled.forEach((card, index) => {
                const cardElement = document.createElement('div');
                cardElement.className = 'sentence-item';
                cardElement.innerHTML = `<strong>Card ${card.cardNumber}:</strong> ${card.text}`;
                cardElement.draggable = true;
                cardElement.dataset.cardNumber = card.cardNumber;
                cardElement.dataset.correctPosition = card.correctPosition;
                
                cardElement.addEventListener('dragstart', handleDragStart);
                cardElement.addEventListener('dragend', handleDragEnd);
                
                sourceContainer.appendChild(cardElement);
            });

            // Create drop zones
            for (let i = 0; i < 4; i++) {
                const dropZone = document.createElement('div');
                dropZone.className = 'drop-zone';
                dropZone.textContent = `Position ${i + 1}`;
                dropZone.dataset.position = i;
                
                dropZone.addEventListener('dragover', handleDragOver);
                dropZone.addEventListener('drop', handleDrop);
                dropZone.addEventListener('dragleave', handleDragLeave);
                
                targetContainer.appendChild(dropZone);
            }
        }

        function handleDragStart(e) {
            e.target.classList.add('dragging');
            e.dataTransfer.setData('text/plain', e.target.textContent);
            e.dataTransfer.setData('application/json', JSON.stringify({
                text: e.target.textContent,
                cardNumber: e.target.dataset.cardNumber,
                correctPosition: e.target.dataset.correctPosition
            }));
        }

        function handleDragEnd(e) {
            e.target.classList.remove('dragging');
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.target.classList.add('drag-over');
        }

        function handleDragLeave(e) {
            e.target.classList.remove('drag-over');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.target.classList.remove('drag-over');
            
            const data = JSON.parse(e.dataTransfer.getData('application/json'));
            e.target.innerHTML = data.text;
            e.target.dataset.cardNumber = data.cardNumber;
            e.target.dataset.correctPosition = data.correctPosition;
            e.target.style.background = '#e8f4fd';
        }

        function checkOrder() {
            const dropZones = document.querySelectorAll('.drop-zone');
            let correct = 0;
            
            dropZones.forEach((zone, index) => {
                if (zone.dataset.correctPosition == index) {
                    zone.style.background = '#d4edda';
                    correct++;
                } else {
                    zone.style.background = '#f8d7da';
                }
            });
            
            alert(`You got ${correct}/4 cards in the correct order!`);
        }

        // Interview Practice Functions
        function setupInterview() {
            const interviewQuestions = [
                {
                    question: "What are your dreams and ambitions for the future?",
                    sampleAnswers: [
                        "I want to become a doctor and help people in need.",
                        "My dream is to start my own business one day.",
                        "I hope to travel around the world and learn about different cultures."
                    ]
                },
                {
                    question: "Have your career interests changed over time?",
                    sampleAnswers: [
                        "Yes, I used to want to be a teacher but now I'm interested in science.",
                        "When I was younger I wanted to be a ballerina, but now I prefer writing.",
                        "My interests have stayed the same - I've always wanted to work with animals."
                    ]
                },
                {
                    question: "What would you do if your first career choice didn't work out?",
                    sampleAnswers: [
                        "I would try something related but different, like J.K. Rowling did.",
                        "I would keep my options open and explore new opportunities.",
                        "I would go back to school and study something else I'm passionate about."
                    ]
                },
                {
                    question: "What qualities do you think are important for success?",
                    sampleAnswers: [
                        "Imagination and creativity, like J.K. Rowling showed.",
                        "Persistence and not giving up when things get difficult.",
                        "Being open to new experiences and opportunities."
                    ]
                },
                {
                    question: "How do you feel about taking risks in life?",
                    sampleAnswers: [
                        "I think calculated risks are necessary for growth and success.",
                        "I'm a bit scared of risks, but I know they can lead to great things.",
                        "I believe in having a backup plan while still taking some risks."
                    ]
                },
                {
                    question: "What did you learn from these success stories?",
                    sampleAnswers: [
                        "Success can come from unexpected places and life changes.",
                        "It's important to follow your passion even if it seems impossible.",
                        "Sometimes our failures lead us to our greatest successes."
                    ]
                }
            ];

            const container = document.getElementById('interview-questions-container');
            interviewQuestions.forEach((q, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question';
                questionDiv.innerHTML = `
                    <h3>Question ${index + 1}</h3>
                    <p><strong>${q.question}</strong></p>
                    <button class="btn" onclick="toggleAnswers(${index})" style="margin: 10px 0;">Show Sample Answers</button>
                    <div id="answers-${index}" style="display: none; margin-top: 15px;">
                        <h4>Sample Answers:</h4>
                        <ul>
                            ${q.sampleAnswers.map(answer => `<li style="margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 5px;">${answer}</li>`).join('')}
                        </ul>
                        <p style="margin-top: 15px; font-style: italic; color: #666;">Now try to answer this question yourself!</p>
                    </div>
                `;
                container.appendChild(questionDiv);
            });
        }

        function toggleAnswers(questionIndex) {
            const answersDiv = document.getElementById(`answers-${questionIndex}`);
            const button = event.target;

            if (answersDiv.style.display === 'none') {
                answersDiv.style.display = 'block';
                button.textContent = 'Hide Sample Answers';
            } else {
                answersDiv.style.display = 'none';
                button.textContent = 'Show Sample Answers';
            }
        }

        // Navigation and Progress Functions
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionId).classList.add('active');
            
            // Update progress bar
            const sections = ['speed-reading', 'comprehension', 'gap-fill', 'capital-letters', 'sentence-order', 'interview', 'final-results'];
            const currentIndex = sections.indexOf(sectionId);
            const progress = ((currentIndex + 1) / sections.length) * 100;
            document.getElementById('progress-bar').style.width = progress + '%';
        }

        function showFinalResults() {
            userScores.total = Math.round((userScores.comprehension + userScores.capital) / 2);

            let message = '';
            if (userScores.total >= 90) {
                message = '🌟 Excellent! You are a reading superstar!';
            } else if (userScores.total >= 80) {
                message = '🎉 Great job! You have strong reading skills!';
            } else if (userScores.total >= 70) {
                message = '👍 Good work! Keep practicing to improve!';
            } else {
                message = '📚 Keep reading and practicing! You can do it!';
            }

            document.getElementById('final-score-display').innerHTML = `
                <h3>${message}</h3>
                <p>Comprehension Score: ${userScores.comprehension}%</p>
                <p>Capital Letters Score: ${userScores.capital}%</p>
                <p><strong>Overall Score: ${userScores.total}%</strong></p>
                <p style="margin-top: 20px; font-style: italic;">Great job completing the Unit 15 - Success activities! You've practiced reading comprehension, grammar, and interview skills about achieving dreams and ambitions.</p>
            `;

            showSection('final-results');
        }

        function restartApp() {
            // Reset all scores
            userScores = { comprehension: 0, capital: 0, total: 0 };
            
            // Reset reading
            resetReading();
            
            // Clear all selections
            document.querySelectorAll('input[type="radio"]').forEach(input => {
                input.checked = false;
                input.parentElement.classList.remove('selected', 'correct', 'incorrect');
            });
            
            // Reset gap fill
            resetGapFill();
            
            // Reset sentence order
            setupSentenceOrder();
            
            // Clear scores
            document.getElementById('comprehension-score').textContent = '';
            document.getElementById('capital-score').textContent = '';
            
            // Go back to first section
            showSection('speed-reading');
        }

        // Initialize the app when page loads
        window.onload = function() {
            initializeApp();
        };
    </script>
</body>
</html>